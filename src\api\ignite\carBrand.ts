/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface CarBrand extends BasicModel<CarBrand> {
  carBrandCode?: string; // 主键
  carBrand?: string; // 汽车品牌
}

export const carBrandList = (params?: CarBrand | any) =>
  defHttp.get<CarBrand>({ url: adminPath + '/carbrand/carBrand/list', params });

export const carBrandListData = (params?: CarBrand | any) =>
  defHttp.post<Page<CarBrand>>({ url: adminPath + '/carbrand/carBrand/listData', params });

export const carBrandForm = (params?: CarBrand | any) =>
  defHttp.get<CarBrand>({ url: adminPath + '/carbrand/carBrand/form', params });

export const carBrandSave = (params?: any, data?: CarBrand | any) =>
  defHttp.postJson<CarBrand>({ url: adminPath + '/carbrand/carBrand/save', params, data });

export const carBrandDelete = (params?: CarBrand | any) =>
  defHttp.get<CarBrand>({ url: adminPath + '/carbrand/carBrand/delete', params });

export const carBrand = (params?: CarBrand | any) =>
  defHttp.get<CarBrand>({ url: adminPath + '/carbrand/carBrand/getBrandList', params });
