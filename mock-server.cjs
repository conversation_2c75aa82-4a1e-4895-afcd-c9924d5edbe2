const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { message } = require('ant-design-vue');

const app = express();
const PORT = 3101;

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cors());
// 用户库
const users = [
  { id: 1, 
    username: 'system',
    password: 'admin', 
    sessionid: 'mock-session-id',
    roles:['admin'],
    permissions:['user:*','system:*'],
    info:{
      company: 'JeeSite',
      version: '4.0',
      title: '管理系统'
    }
  },
];
//菜单数据
const menus={
  menus: [
    {
      redirect: "/sys/menu/index",
      path: "/sys/menu/index",
      component: "LAYOUT",
      children: [
        {
          path: "/sys/menu/index",
          component: "/sys/menu/index",
          meta: {
            hideMenu: false,
            color: "",
            icon: "icon-book-open",
            title: "菜单管理"
          },
          name: "ViewsSysMenuIndex",
          id: "1742788875691663360",
          leaf: true,
          url: "/sys/menu/index",
          props: null,
          target: null
        }
      ],
      meta: {
        hideMenu: false,
        color: "",
        icon: "icon-settings",
        title: "系统设置"
      },
      name: "Views1742788875649720320",
      id: "1742788875649720320",
      leaf: false,
      url: "",
      props: null,
      target: null
    }
  ],
  userMenus: {
    'user1': ['1742788875649720320', '1742788875691663360']
  }
}
// 登录接口
app.post('/a/login', (req, res) => {
  // 这里可以加 base64 解码逻辑
  const { username, password } = req.body;
  const user = users.find((u) => u.username === username && u.password === password);
  if (user) {
    res.json({
      result: 'success',
      message: '登录成功',
      sessionid: user.sessionid,
      isValidCodeLogin:false,
      user: {
        id: user.id,
        username: user.username,
        ...user.info,
      },
    });
  } else {
    res.status(401).json({
      result: 'error',
      message: '用户名或密码错误'
    });
  }
});

// 用户信息接口
app.get('/a/index', (req, res) => {
  res.json({
    result: 'true',
    message: '获取用户信息成功',
    sessionid: 'mock-session-id',
    user: {
      id: 1,
      username: 'system',
    }
  });
});
//用户信息
app.get('/a/authInfo',(req,res)=>{
  const userId=req.headers['x-user-id']||1
  const user=users.find(u=>u.id===Number(userId))
  res.json({
    roles:user?.roles||['employee'],
    stringPermissions:user?.permissions||[]
  })
})
//菜单路由
app.get('/a/menu/index',(req,res)=>{
  try{
    res.json({
      result:'true',
      code:200,
      message:'success',
      data:menus.menus
    })
  }catch{
    res.status(500).json({
      code:500,
      message:'error',
      data:null
    })
  }
})
app.listen(PORT, () => {
  console.log(`Mock server running at http://localhost:${PORT}`);
});