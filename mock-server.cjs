const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');

const app = express();
const PORT = 3101;

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cors());
// 用户库
const users = [
  { id: 1, 
    username: 'system', 
    password: 'admin', 
    sessionid: 'mock-session-id',
    roles:['admin'],
    permissions:['user:*','system:*'],
    info:{
      company: 'JeeSite',
      version: '4.0',
      title: '管理系统'
    }
  },
];
// 登录接口
app.post('/a/login', (req, res) => {
  // 这里可以加 base64 解码逻辑
  const { username, password } = req.body;
  const user = users.find(u => u.username === username && u.password === password);
  if (user) {
    res.json({
      result: 'success',
      message: '登录成功',
      sessionid: user.sessionid,
      isValidCodeLogin:false,
      user: {
        id: user.id,
        username: user.username,
        ...user.info
      }
    });
  } else {
    res.status(401).json({
      result: 'error',
      message: '用户名或密码错误'
    });
  }
});

// 用户信息接口
app.get('/a/index', (req, res) => {
  res.json({
    result: 'true',
    message: '获取用户信息成功',
    sessionid: 'mock-session-id',
    user: {
      id: 1,
      username: 'system'
    }
  });
});
//用户信息
app.get('/a/authInfo',(req,res)=>{
  const userId=req.headers['x-user-id']||1
  const user=users.find(u=>u.id===Number(userId))
  res.json({
    roles:user?.roles||['employee'],
    stringPermissions:user?.permissions||[]
  })
})
//菜单路由
app.get('/a/menuRoute',(req,res)=>{
  res.json([
    {
      name: 'Dashboard',
      path: '/dashboard',
      component: 'LAYOUT',
      redirect: '/dashboard/analysis',
      meta: {
        title: 'routes.dashboard.dashboard',
        icon: 'ion:grid-outline',
        orderNo: 10,
      },
      children: [
        {
          name: 'Analysis',
          path: '/dashboard/analysis',
          component: '/dashboard/analysis/index',
          meta: {
            title: 'routes.dashboard.analysis',
            hideMenu: false,
          },
        },
        {
          name: 'Workbench',
          path: '/dashboard/workbench',
          component: '/dashboard/workbench/index',
          meta: {
            title: 'routes.dashboard.workbench',
            hideMenu: false,
          },
        },
      ],
    },
    {
      name: 'System',
      path: '/system',
      component: 'LAYOUT',
      redirect: '/system/account',
      meta: {
        title: 'routes.demo.system.moduleName',
        icon: 'ion:settings-outline',
        orderNo: 2000,
      },
      children: [
        {
          name: 'AccountManagement',
          path: '/system/account',
          component: '/demo/system/account/index',
          meta: {
            title: 'routes.demo.system.account',
            hideMenu: false,
          },
        },
        {
          name: 'RoleManagement',
          path: '/system/role',
          component: '/demo/system/role/index',
          meta: {
            title: 'routes.demo.system.role',
            hideMenu: false,
          },
        },
        {
          name: 'MenuManagement',
          path: '/system/menu',
          component: '/demo/system/menu/index',
          meta: {
            title: 'routes.demo.system.menu',
            hideMenu: false,
          },
        },
        {
          name: 'DeptManagement',
          path: '/system/dept',
          component: '/demo/system/dept/index',
          meta: {
            title: 'routes.demo.system.dept',
            hideMenu: false,
          },
        },
      ],
    },
    {
      name: 'Permission',
      path: '/permission',
      component: 'LAYOUT',
      redirect: '/permission/front/page',
      meta: {
        title: 'routes.demo.permission.permission',
        icon: 'ion:key-outline',
        orderNo: 15,
      },
      children: [
        {
          name: 'PermissionFrontDemo',
          path: '/permission/front',
          component: 'LAYOUT',
          redirect: '/permission/front/page',
          meta: {
            title: 'routes.demo.permission.front',
          },
          children: [
            {
              name: 'PermissionFrontPage',
              path: '/permission/front/page',
              component: '/demo/permission/front/index',
              meta: {
                title: 'routes.demo.permission.frontPage',
              },
            },
            {
              name: 'PermissionFrontBtn',
              path: '/permission/front/btn',
              component: '/demo/permission/front/Btn',
              meta: {
                title: 'routes.demo.permission.frontBtn',
              },
            },
          ],
        },
        {
          name: 'PermissionBackDemo',
          path: '/permission/back',
          component: 'LAYOUT',
          redirect: '/permission/back/page',
          meta: {
            title: 'routes.demo.permission.back',
          },
          children: [
            {
              name: 'PermissionBackPage',
              path: '/permission/back/page',
              component: '/demo/permission/back/index',
              meta: {
                title: 'routes.demo.permission.backPage',
              },
            },
            {
              name: 'PermissionBackBtn',
              path: '/permission/back/btn',
              component: '/demo/permission/back/Btn',
              meta: {
                title: 'routes.demo.permission.backBtn',
              },
            },
          ],
        },
      ],
    },
    {
      name: 'Level',
      path: '/level',
      component: 'LAYOUT',
      redirect: '/level/menu1/menu1-1',
      meta: {
        title: 'routes.demo.level.level',
        icon: 'ion:menu-outline',
        orderNo: 1000,
      },
      children: [
        {
          name: 'Menu1',
          path: '/level/menu1',
          component: 'LAYOUT',
          redirect: '/level/menu1/menu1-1',
          meta: {
            title: 'Menu1',
          },
          children: [
            {
              name: 'Menu11',
              path: '/level/menu1/menu1-1',
              component: '/demo/level/Menu11',
              meta: {
                title: 'Menu1-1',
              },
            },
            {
              name: 'Menu12',
              path: '/level/menu1/menu1-2',
              component: 'LAYOUT',
              redirect: '/level/menu1/menu1-2/menu1-2-1',
              meta: {
                title: 'Menu1-2',
              },
              children: [
                {
                  name: 'Menu121',
                  path: '/level/menu1/menu1-2/menu1-2-1',
                  component: '/demo/level/Menu121',
                  meta: {
                    title: 'Menu1-2-1',
                  },
                },
              ],
            },
          ],
        },
        {
          name: 'Menu2',
          path: '/level/menu2',
          component: '/demo/level/Menu2',
          meta: {
            title: 'Menu2',
          },
        },
      ],
    },
  ])
})
app.listen(PORT, () => {
  console.log(`Mock server running at http://localhost:${PORT}`);
});