const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');

const app = express();
const PORT = 3101;

app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(cors());
// 用户库
const users = [
  { id: 1, 
    username: 'system', 
    password: 'admin', 
    sessionid: 'mock-session-id',
    roles:['admin'],
    permissions:['user:*','system:*'],
    info:{
      company: 'JeeSite',
      version: '4.0',
      title: '管理系统'
    }
  },
];
// 登录接口
app.post('/a/login', (req, res) => {
  // 这里可以加 base64 解码逻辑
  const { username, password } = req.body;
  const user = users.find(u => u.username === username && u.password === password);
  if (user) {
    res.json({
      result: 'success',
      message: '登录成功',
      sessionid: user.sessionid,
      isValidCodeLogin:false,
      user: {
        id: user.id,
        username: user.username,
        ...user.info
      }
    });
  } else {
    res.status(401).json({
      result: 'error',
      message: '用户名或密码错误'
    });
  }
});

// 用户信息接口
app.get('/a/index', (req, res) => {
  res.json({
    result: 'true',
    message: '获取用户信息成功',
    sessionid: 'mock-session-id',
    user: {
      id: 1,
      username: 'system'
    }
  });
});
//用户信息
app.get('/a/authInfo',(req,res)=>{
  const userId=req.headers['x-user-id']||1
  const user=users.find(u=>u.id===Number(userId))
  res.json({
    roles:user?.roles||['employee'],
    stringPermissions:user?.permissions||[]
  })
})
//菜单路由
app.get('/a/menuRoute',(req,res)=>{
  res.json([
  ])
})
// 兼容 /js/a/login
// app.post('/js/a/login', (req, res) => {
//   const { username, password } = req.body;
//   const user = users.find(u => u.username === username && u.password === password);
//   if (user) {
//     res.json({
//       result: 'success',
//       message: '登录成功',
//       sessionid: user.sessionid,
//       user: {
//         id: user.id,
//         username: user.username
//       }
//     });
//   } else {
//     res.status(401).json({
//       result: 'error',
//       message: '用户名或密码错误'
//     });
//   }
// });

// 兼容 /js/a/index
// app.get('/js/a/index', (req, res) => {
//   res.json({
//     result: 'success',
//     message: '获取用户信息成功',
//     sessionid: 'mock-session-id',
//     user: {
//       id: 1,
//       username: 'system'
//     }
//   });
// });

app.listen(PORT, () => {
  console.log(`Mock server running at http://localhost:${PORT}`);
});