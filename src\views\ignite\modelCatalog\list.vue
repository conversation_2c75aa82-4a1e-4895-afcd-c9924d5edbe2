<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'modelcatalog:modelCatalog:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ catalogCode: record.catalogCode })">
          {{ record.modelNum }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsModelcatalogModelCatalogList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { modelCatalogDelete, modelCatalogListData } from '/@/api/ignite/modelCatalog';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('modelcatalog.modelCatalog');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('车型型录管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
      {
        label: t('模型编号'),
        field: 'modelNum',
        component: 'Input',
      },
      {
        label: t('车型'),
        field: 'carType',
        component: 'Input',
        componentProps: {
          dictType: '',
          allowClear: true,
        },
      },
      {
        label: t('涂装/颜色'),
        field: 'painting',
        component: 'Input',
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('模型编号'),
      dataIndex: 'modelNum',
      key: 'a.model_num',
      sorter: true,
      width: 130,
      slot: 'firstColumn',
      align: 'center',
    },
    {
      title: t('模型系列'),
      dataIndex: 'modelSeries',
      key: 'a.model_series',
      sorter: true,
      width: 230,
      align: 'center',
      dictType: 'model_series',
    },
    {
      title: t('车型'),
      dataIndex: 'carType',
      key: 'a.car_type',
      sorter: true,
      width: 160,
      align: 'center',
      dictType: '',
      // 添加自定义渲染函数
      customRender: ({ record }) => {
        return `${record.carType || ''}${record.carBir ? `-${record.carBir}` : ''}`;
      }
    },
    {
      title: t('涂装/颜色'),
      dataIndex: 'painting',
      key: 'a.painting',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('零售价'),
      dataIndex: 'saleMoney',
      key: 'a.sale_money',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('允许折扣'),
      dataIndex: 'saleDiscount',
      key: 'a.sale_discount',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('数量'),
      dataIndex: 'limitedQuantity',
      key: 'a.limited_quantity',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('发售区域'),
      dataIndex: 'sealsArea',
      key: 'a.seals_area',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑车型型录'),
        onClick: handleForm.bind(this, { catalogCode: record.catalogCode }),
        auth: 'modelcatalog:modelCatalog:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除车型型录'),
        popConfirm: {
          title: t('是否确认删除车型型录'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'modelcatalog:modelCatalog:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: modelCatalogListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const [registerDrawer, { openDrawer }] = useDrawer();

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { catalogCode: record.catalogCode };
    const res = await modelCatalogDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
