<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'cartype:carType:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
<!--        <a @click="handleForm({ carTypeCode: record.carTypeCode })">-->
<!--          {{ record.carBrandCode }}-->
<!--        </a>-->
        <a @click="handleForm({ carTypeCode: record.carTypeCode })">
          {{ record.carBrand }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsCartypeCarTypeList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { carTypeDelete, carTypeListData } from '/@/api/ignite/carType';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('cartype.carType');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('车型管理表管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
      {
        label: t('品牌'),
        field: 'carBrand',
          component: 'Input',
      },
      {
        label: t('车型'),
        field: 'carType',
        component: 'Input',
      },
      {
        label: t('年份'),
        field: 'carBir',
        component: 'Input',
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('品牌'),
      dataIndex: 'carBrand',
      key: 'a.car_brand',
      sorter: true,
      width: 230,
      align: 'center',
      dictType: '',
      slot: 'firstColumn',
    },
    {
      title: t('车型'),
      dataIndex: 'carType',
      key: 'a.car_type',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('年份'),
      dataIndex: 'carBir',
      key: 'a.car_bir',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑车型管理表'),
        onClick: handleForm.bind(this, { carTypeCode: record.carTypeCode}),
        auth: 'cartype:carType:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除车型管理表'),
        popConfirm: {
          title: t('是否确认删除车型管理表'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'cartype:carType:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: carTypeListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const [registerDrawer, { openDrawer }] = useDrawer();

  function handleForm(record: Recordable) {
    console.log('传递给表单的数据:', record);
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { carTypeCode: record.carTypeCode };
    const res = await carTypeDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
