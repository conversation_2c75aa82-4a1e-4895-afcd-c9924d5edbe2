{"name": "jeesite", "version": "5.10.0", "type": "module", "private": true, "scripts": {"bootstrap": "pnpm install", "serve": "npm run dev", "dev": "vite", "build": "vite build --mode production", "build:tomcat": "vite build --mode tomcat --emptyOutDir", "build:preview": "npm run build && npm run preview:dist", "preview": "npm run build && npm run preview:dist", "preview:dist": "vite preview --mode development --port 3100", "report": "cross-env REPORT=true npm run build", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint:eslint": "eslint --cache --max-warnings 0  \"./**/*.{ts,tsx,vue}\" --fix", "lint:prettier": "prettier --write \"./**/*.{vue,tsx,less,scss}\"", "lint:stylelint": "stylelint \"./**/*.{vue,less,scss,css}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:all": "npm run type:check && npm run lint:eslint && npm run lint:prettier && npm run lint:stylelint", "reinstall": "rimraf pnpm-lock.yaml yarn.lock package-lock.json node_modules; npm run bootstrap", "update": "ncu -u -x eslint,codemirror && npm run reinstall", "mock": "json-server db.json --port 3101"}, "dependencies": {"@ant-design/colors": "7.1.0", "@ant-design/icons-vue": "7.0.1", "@vue/runtime-core": "3.5.12", "@vue/shared": "3.5.12", "@vueuse/core": "11.1.0", "@vueuse/shared": "11.1.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@zxcvbn-ts/core": "3.0.4", "ant-design-vue": "4.2.5", "axios": "1.7.7", "body-parser": "^2.2.0", "codemirror": "5.65.16", "cors": "^2.8.5", "cropperjs": "1.6.2", "crypto-js": "4.2.0", "dayjs": "1.11.13", "echarts": "5.5.1", "express": "^5.1.0", "intro.js": "7.2.0", "lodash-es": "4.17.21", "nprogress": "0.2.0", "path-to-regexp": "8.2.0", "pinia": "2.2.4", "print-js": "1.6.0", "qrcode": "1.5.4", "qs": "6.13.0", "resize-observer-polyfill": "1.5.1", "showdown": "2.1.0", "sortablejs": "1.15.3", "spark-md5": "3.0.2", "terser": "5.36.0", "vditor": "3.10.7", "vue": "3.5.12", "vue-i18n": "10.0.4", "vue-json-pretty": "2.4.0", "vue-router": "4.4.5", "vue-types": "5.1.3", "xlsx": "0.18.5"}, "devDependencies": {"@iconify/iconify": "3.1.1", "@iconify/json": "2.2.263", "@iconify/utils": "2.1.33", "@types/codemirror": "5.60.15", "@types/crypto-js": "4.2.2", "@types/fs-extra": "11.0.4", "@types/intro.js": "5.1.5", "@types/lodash-es": "4.17.12", "@types/node": "22.7.9", "@types/nprogress": "0.2.3", "@types/qrcode": "1.5.5", "@types/qs": "6.9.16", "@types/showdown": "2.0.6", "@types/sortablejs": "1.15.8", "@typescript-eslint/eslint-plugin": "8.11.0", "@typescript-eslint/parser": "8.11.0", "@unocss/eslint-config": "0.63.6", "@vitejs/plugin-legacy": "5.4.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/compiler-sfc": "3.5.12", "@vue/test-utils": "2.4.6", "autoprefixer": "10.4.20", "concurrently": "^9.2.0", "cross-env": "7.0.3", "cz-git": "1.10.1", "czg": "1.10.1", "dotenv": "16.4.5", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.29.1", "esno": "4.8.0", "fs-extra": "11.2.0", "json-server": "^1.0.0-beta.3", "less": "4.2.0", "lint-staged": "15.2.10", "npm-run-all": "4.1.5", "picocolors": "1.1.1", "pkg-types": "1.2.1", "postcss": "8.4.47", "postcss-html": "1.7.0", "postcss-less": "6.0.0", "prettier": "3.3.3", "prettier-plugin-packagejson": "2.5.3", "rimraf": "6.0.1", "rollup": "4.24.0", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "16.10.0", "stylelint-config-recommended-less": "3.0.1", "stylelint-config-recommended-vue": "1.5.0", "stylelint-config-standard": "36.0.1", "stylelint-config-standard-less": "3.0.1", "stylelint-order": "6.0.4", "stylelint-prettier": "5.0.2", "tinycolor2": "^1.6.0", "ts-node": "10.9.2", "typescript": "5.6.3", "unocss": "0.63.6", "vite": "5.4.10", "vite-plugin-compression": "0.5.1", "vite-plugin-html": "3.2.2", "vite-plugin-mkcert": "1.17.6", "vite-plugin-theme-vite3": "1.0.5", "vite-plugin-vue-setup-extend": "0.4.0", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.1.6"}, "keywords": ["typescript", "jeesite", "antdv", "vite", "vue"], "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "homepage": "https://jeesite.com", "repository": {"type": "git", "url": "https://gitee.com/thinkgem/jeesite-vue.git"}, "bugs": {"url": "https://gitee.com/thinkgem/jeesite-vue/issues"}, "author": {"name": "ThinkGem", "email": "<EMAIL>", "url": "https://gitee.com/thinkgem"}, "engines": {"node": "18 || >=20"}, "packageManager": "pnpm@9.12.1"}