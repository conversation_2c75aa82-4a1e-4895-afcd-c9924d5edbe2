<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'agent:agent:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ agentCode: record.agentCode })">
          {{ record.agentName }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsAgentAgentList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { agentDelete, agentListData } from '/@/api/ignite/agent';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('agent.agent');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('代理管理管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
      {
        label: t('代理商名'),
        field: 'agentName',
        component: 'Input',
      },
      {
        label: t('代理商手机'),
        field: 'agentPhone',
        component: 'Input',
      },
      {
        label: t('代理商积分'),
        field: 'agentPoints',
        component: 'Input',
      },
      {
        label: t('代理地址'),
        field: 'agentAddress',
        component: 'Input',
      },
      {
        label: t('加盟时间'),
        field: 'joinDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD',
          showTime: false,
        },
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_search_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('代理商名'),
      dataIndex: 'agentName',
      key: 'a.agent_name',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('代理商手机'),
      dataIndex: 'agentPhone',
      key: 'a.agent_phone',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('代理商积分'),
      dataIndex: 'agentPoints',
      key: 'a.agent_points',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('代理地址'),
      dataIndex: 'agentAddress',
      key: 'a.agent_address',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('加盟时间'),
      dataIndex: 'joinDate',
      key: 'a.join_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑代理管理'),
        onClick: handleForm.bind(this, { agentCode: record.agentCode }),
        auth: 'agent:agent:edit',
      },
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        title: t('删除代理管理'),
        popConfirm: {
          title: t('是否确认删除代理管理'),
          confirm: handleDelete.bind(this, record),
        },
        auth: 'agent:agent:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: agentListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const [registerDrawer, { openDrawer }] = useDrawer();

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { agentCode: record.agentCode };
    const res = await agentDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
