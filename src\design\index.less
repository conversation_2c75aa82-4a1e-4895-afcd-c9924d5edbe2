@import './transition/index.less';
@import './var/index.less';
@import './ant/index.less';
@import './public.less';
@import './theme.less';
@import './entry.less';

input:-webkit-autofill {
  box-shadow: 0 0 0 1000px white inset !important;
}

:-webkit-autofill {
  transition: background-color 5000s ease-in-out 0s !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  overflow-x: hidden !important;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none !important;
}

.table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  border: 1px solid #f0f0f0;

  th,
  td {
    padding: 8px;
    border: 1px solid #f0f0f0;
    vertical-align: middle;
  }

  th {
    font-weight: normal;
    background-color: #fafafa;
  }

  tr:hover {
    background-color: #fafafa;

    .table {
      background-color: #fff;
    }
  }
}

.ant-tabs {
  .ant-tabs-tab-btn {
    color: fade(@text-color-base, 75);
  }
}

.ant-card {
  .ant-card-head-title {
    font-weight: normal;
  }
}
