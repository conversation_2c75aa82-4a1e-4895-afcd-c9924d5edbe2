<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'modelcatalog:modelCatalog:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsModelcatalogModelCatalogForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { ModelCatalog, modelCatalogSave, modelCatalogForm } from '/@/api/ignite/modelCatalog';
  import { carType } from '/@/api/ignite/carType';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('modelcatalog.modelCatalog');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<ModelCatalog>({} as ModelCatalog);

  // 存储汽车品牌选项
  const carTypeOptions = ref<{ label: string; value: string }[]>([]);
  const typeSelectOptions = computed(() => carTypeOptions.value);

  // 获取汽车品牌数据
  async function fetchCarType() {
    try {
      const response = await carType();
      const brandsData = response.carTypeList || [];
      carTypeOptions.value = brandsData.map((type) => ({
        label: `${type.carType || ''}${type.carBir ? `-${type.carBir}` : ''}`,
        value: type.carTypeCode,
      }));
    } catch (error) {
      carTypeOptions.value = [];
    }
  }

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增模型型录') : t('编辑模型型录'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('模型系列'),
      field: 'modelSeries',
      component: 'Select',
      componentProps: {
        placeholder: t('请选择打印类型'),
        dictType: 'model_series',
        allowClear: true,
      },
    },
    {
      label: t('模型编号'),
      field: 'modelNum',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        {
          pattern: /^\d{4}}$/,
          message: t('请输入一个四位正整数，例0001、0596'),
          required: true,
        },
      ],
    },
    {
      label: t('车型'),
      field: 'carTypeCode',
      component: 'Select',
      componentProps: ({ formModel }) => ({
        options: typeSelectOptions.value,
        placeholder: t('请选择汽车品牌'),
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      }),
      rules: [
        {
          required: true,
          message: t('请选择车型'),
        },
      ],
      colProps: { span: 8 }, // 布局设置
    },
    {
      label: t('涂装/颜色'),
      field: 'painting',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        {
          required: true,
          message: t('请输入涂装/颜色'),
        },
      ],
    },
    {
      label: t('零售价'),
      field: 'saleMoney',
      component: 'Input',
      componentProps: {
        maxlength: 6,
      },
      rules: [
        { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') },
      ],
    },
    {
      label: t('允许折扣'),
      field: 'saleDiscount',
      component: 'Input',
      componentProps: {
        maxlength: 2,
      },
      rules: [
        { pattern: /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/, message: t('请输入一个数值') },
      ],
    },
    {
      label: t('发售数量'),
      field: 'limitedQuantity',
      component: 'Input',
      componentProps: {
        maxlength: 9,
      },
      rules: [{ pattern: /^\d+$/, message: t('请输入一个正整数') }],
    },
    {
      label: t('发售区域'),
      field: 'sealsArea',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 64,
      },
      colProps: { md: 24, lg: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 12 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    await fetchCarType();
    const res = await modelCatalogForm(data);
    record.value = (res.modelCatalog || {}) as ModelCatalog;
    record.value.__t = new Date().getTime();
    setFieldsValue({
      ...record.value,
      carType: record.value.carType?.toString() || null,
    });
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        catalogCode: record.value.catalogCode,
      };
      const res = await modelCatalogSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
