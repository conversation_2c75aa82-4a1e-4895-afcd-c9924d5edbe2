<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'trackmodel:trackModel:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsTrackmodelTrackModelForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { TrackModel, trackModelSave, trackModelForm } from '/@/api/ignite/trackModel';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('trackmodel.trackModel');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<TrackModel>({} as TrackModel);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增模型追踪码信息') : t('编辑模型追踪码信息'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('经销商编码'),
      field: 'agentCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('件编码'),
      field: 'pieceCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('模型编码'),
      field: 'modelCode',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('批次'),
      field: 'batch',
      component: 'Input',
      componentProps: {
        maxlength: 2,
      },
    },
    {
      label: t('追溯码信息'),
      field: 'trackInformation',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('打包时间'),
      field: 'packDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('发货时间'),
      field: 'shipDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD HH:mm',
        showTime: { format: 'HH:mm' },
      },
    },
    {
      label: t('追溯码状态'),
      field: 'trackState',
      component: 'Input',
      componentProps: {
        maxlength: 1,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { md: 24, lg: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await trackModelForm(data);
    record.value = (res.trackModel || {}) as TrackModel;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        trackCode: record.value.trackCode,
      };
      // console.log('submit', params, data, record);
      const res = await trackModelSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
