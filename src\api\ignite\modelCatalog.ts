/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface ModelCatalog extends BasicModel<ModelCatalog> {
  catalogCode?: string; // 主键
  modelSeries?: string; // 模型系列
  modelNum?: string; // 模型编号
  carTypeCode?: string; // 车型id
  carType?: string; // 车型
  carBir?: string; // 车型日期
  painting?: string; // 涂装/颜色
  saleMoney?: number; // 零售价
  saleDiscount?: number; // 允许折扣
  limitedQuantity?: number; // 数量
  sealsArea?: string; // 发售区域
}

export const modelCatalogList = (params?: ModelCatalog | any) =>
  defHttp.get<ModelCatalog>({ url: adminPath + '/modelcatalog/modelCatalog/list', params });

export const modelCatalogListData = (params?: ModelCatalog | any) =>
  defHttp.post<Page<ModelCatalog>>({ url: adminPath + '/modelcatalog/modelCatalog/listData', params });

export const modelCatalogForm = (params?: ModelCatalog | any) =>
  defHttp.get<ModelCatalog>({ url: adminPath + '/modelcatalog/modelCatalog/form', params });

export const modelCatalogSave = (params?: any, data?: ModelCatalog | any) =>
  defHttp.postJson<ModelCatalog>({ url: adminPath + '/modelcatalog/modelCatalog/save', params, data });

export const modelCatalogDelete = (params?: ModelCatalog | any) =>
  defHttp.get<ModelCatalog>({ url: adminPath + '/modelcatalog/modelCatalog/delete', params });

export const modelCatalog = (params?: ModelCatalog | any) =>
  defHttp.get<ModelCatalog>({ url: adminPath + '/modelcatalog/modelCatalog/getModelCatalogList', params });
