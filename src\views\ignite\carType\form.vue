<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'cartype:carType:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsCartypeCarTypeForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { CarType, carTypeSave, carTypeForm } from '/@/api/ignite/carType';
  import { carBrand } from '/@/api/ignite/carBrand';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('cartype.carType');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<CarType>({} as CarType);

  // 存储汽车品牌选项
  const carBrandOptions = ref<{ label: string; value: string }[]>([]);
  const brandSelectOptions = computed(() => carBrandOptions.value);

  // 获取汽车品牌数据
  async function fetchCarBrands() {
    try {
      const response = await carBrand();
      const brandsData = response.carBrandList || [];
      carBrandOptions.value = brandsData.map((brand) => ({
        label: brand.carBrand,
        value: brand.carBrandCode,
      }));
    } catch (error) {
      carBrandOptions.value = [];
    }
  }

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增车型管理表') : t('编辑车型管理表'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('品牌'),
      field: 'carBrandCode',
      fieldLabel: 'carBrand',
      component: 'Select',
      componentProps: ({ formModel }) => ({
        options: brandSelectOptions.value,
        placeholder: t('请选择汽车品牌'),
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      }),
      rules: [
        {
          required: true,
          message: t('请选择汽车品牌'),
        },
      ],
      colProps: { span: 8 }, // 布局设置
    },
    {
      label: t('车型'),
      field: 'carType',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('年份'),
      field: 'carBir',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
      rules: [
        {
          pattern: /^(19|20)\d{2}$/, // 1900-2099年
          message: t('请输入有效的年份 (1900-2099)'),
          trigger: 'blur',
        },
      ],
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { md: 24, lg: 24 },
    },
  ];
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 12 },
  });

  // 加载品牌下拉框数据
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    // 每次打开都刷新品牌数据
    await fetchCarBrands();
    const res = await carTypeForm(data);
    record.value = (res.carType || {}) as CarType;
    record.value.__t = new Date().getTime();
    await setFieldsValue({
      ...record.value,
      carBrand: record.value.carBrand?.toString() || null,
    });
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const formData = await validate();
      setDrawerProps({ confirmLoading: true });

      const params: any = {
        isNewRecord: record.value.isNewRecord,
        carTypeCode: record.value.carTypeCode,
        ...formData,
      };
      const res = await carTypeSave(params);
      showMessage(res.message);
      closeDrawer();
      emit('success', formData);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
