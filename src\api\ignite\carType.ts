/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';
import { CarBrand } from '/@/api/ignite/carBrand';

const { adminPath } = useGlobSetting();

export interface CarType extends BasicModel<CarType> {
  carTypeCode?: string; // 主键
  carBrandCode?: string; // 品牌
  carBrand?: string; // 品牌
  carType?: string; // 车型
  carBir?: string; // 年份
}

export const carTypeList = (params?: CarType | any) =>
  defHttp.get<CarType>({ url: adminPath + '/cartype/carType/list', params });

export const carTypeListData = (params?: CarType | any) =>
  defHttp.post<Page<CarType>>({ url: adminPath + '/cartype/carType/listData', params });

export const carTypeForm = (params?: CarType | any) =>
  defHttp.get<CarType>({ url: adminPath + '/cartype/carType/form', params });

export const carTypeSave = (params?: any, data?: CarType | any) =>
  defHttp.postJson<CarType>({ url: adminPath + '/cartype/carType/save', params, data });

export const carTypeDelete = (params?: CarType | any) =>
  defHttp.get<CarType>({ url: adminPath + '/cartype/carType/delete', params });

export const carType = (params?: CarType | any) =>
  defHttp.get<CarType>({ url: adminPath + '/cartype/carType/getTypeList', params });
