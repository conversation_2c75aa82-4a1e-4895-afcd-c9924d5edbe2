html[data-theme='dark'] {
  .ant-pagination.ant-pagination {
    &-mini {
      .ant-pagination-prev,
      .ant-pagination-next,
      .ant-pagination-item {
        background-color: rgb(255 255 255 / 4%) !important;

        a {
          color: #8b949e !important;
        }
      }

      .ant-select-arrow {
        color: @text-color-secondary !important;
      }

      .ant-pagination-item-active {
        // background-color: @primary-color !important;
        background: fade(#2a50ed, 85) !important;
        border: none;
        border-radius: none !important;

        a {
          color: @white !important;
        }
      }
    }

    .ant-select-status-error {
      .ant-select-selector {
        border-color: #424242 !important;
      }
    }
  }
}

.ant-pagination.ant-pagination {
  &-mini {
    .ant-pagination-prev,
    .ant-pagination-next {
      font-size: 12px;
      color: @text-color-base;
      border: 1px solid;
    }

    .ant-pagination-prev:hover,
    .ant-pagination-next:hover,
    .ant-pagination-item:focus,
    .ant-pagination-item:hover {
      a {
        color: @primary-color;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item {
      margin: 0 4px !important;
      background-color: #f4f4f5 !important;
      border: none;
      border-radius: 4px !important;

      a {
        margin-top: 1px;
        color: #606266;
      }

      &:last-child {
        margin-right: 0 !important;
      }
    }

    .ant-pagination-item-active {
      background-color: @primary-color !important;
      border: none;
      border-radius: none !important;

      a {
        color: @white !important;
      }
    }

    .ant-pagination-options {
      margin-left: 12px;
    }

    .ant-pagination-options-quick-jumper input {
      height: 22px;
      margin: 0 6px;
      line-height: 22px;
      text-align: center;
    }

    .ant-select-arrow {
      color: @border-color-shallow-dark;
    }
  }

  .ant-select.ant-select-in-form-item {
    width: auto;
  }

  &-disabled {
    display: none !important;
  }

  .ant-select-status-error {
    .ant-select-selector {
      border-color: @border-color-base !important;
    }
  }

  .ant-select-focused.ant-select-status-error {
    .ant-select-selector {
      box-shadow: 0 0 0 2px fade(@border-color-base, 10%) !important;
    }
  }
}
