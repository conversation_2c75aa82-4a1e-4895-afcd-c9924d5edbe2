/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface TrackBox extends BasicModel<TrackBox> {
  trackInformationCode?: string; // 主键
  modelCode?: string; // 模型编码
  batch?: string; // 批次
  trackInformation?: string; // 编码信息
  agentCode?: string; // 代理编码
}

export interface TrackBoxList extends BasicModel<TrackBoxList> {
  agentCode?: string; // 代理编码
  trackBoxList?: string[]; // 箱编码信息
}

export const trackBoxList = (params?: TrackBox | any) =>
  defHttp.get<TrackBox>({ url: adminPath + '/trackbox/trackBox/list', params });

export const trackBoxListData = (params?: TrackBox | any) =>
  defHttp.post<Page<TrackBox>>({ url: adminPath + '/trackbox/trackBox/listData', params });

export const trackBoxForm = (params?: TrackBox | any) =>
  defHttp.get<TrackBox>({ url: adminPath + '/trackbox/trackBox/form', params });

export const trackBoxSave = (params?: any, data?: TrackBox | any) =>
  defHttp.postJson<TrackBox>({ url: adminPath + '/trackbox/trackBox/save', params, data });

export const trackBoxDelete = (params?: TrackBox | any) =>
  defHttp.get<TrackBox>({ url: adminPath + '/trackbox/trackBox/delete', params });

export const trackBoxSendGoods = (params?: any, data?: TrackBoxList | any) =>
  defHttp.postJson<TrackBoxList>({ url: adminPath + '/trackbox/trackBox/bindTrackBoxAgent', params, data });

export const trackBoxWithoutAgent = (params?: TrackBox | any) =>
  defHttp.get<TrackBox>({ url: adminPath + '/trackbox/trackBox/getTrackBoxNullAgentList', params });
