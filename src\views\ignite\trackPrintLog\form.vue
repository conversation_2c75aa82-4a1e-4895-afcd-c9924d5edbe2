<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'trackprintlog:trackPrintLog:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsTrackprintlogTrackPrintLogForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { TrackPrintLog, trackPrintLogSave, trackPrintLogForm } from '/@/api/ignite/trackPrintLog';
  import { printer } from '/@/api/ignite/printer';
  import { modelCatalog } from '/@/api/ignite/modelCatalog';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('trackprintlog.trackPrintLog');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<TrackPrintLog>({} as TrackPrintLog);

  // 存储打印机选项
  const printerOptions = ref<{ label: string; value: string }[]>([]);
  const printerSelectOptions = computed(() => printerOptions.value);

  // 存储打印机选项
  const modelCatalogOptions = ref<{ label: string; value: string }[]>([]);
  const modelCatalogSelectOptions = computed(() => modelCatalogOptions.value);

  // 获取打印机数据
  async function fetchPrinter() {
    try {
      const response = await printer();
      const printerData = response.printerList || [];
      printerOptions.value = printerData.map((type) => ({
        label: type.printerName,
        value: type.printerCode,
      }));
    } catch (error) {
      printerOptions.value = [];
    }
  }

  // 获取模型数据
  async function fetchModelCatalog() {
    try {
      const response = await modelCatalog();
      const modelCatalogData = response.modelCatalogVOList || [];
      modelCatalogOptions.value = modelCatalogData.map((type) => ({
        label: `${type.modelNum || ''}${type.carType ? `-${type.carType}` : ''}${type.painting ? `-${type.painting}` : ''}`,
        value: type.catalogCode,
      }));
    } catch (error) {
      modelCatalogOptions.value = [];
    }
  }

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增打印项目') : t('编辑打印记录信息'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('打印机'),
      field: 'printerCode',
      component: 'Select',
      componentProps: ({ formModel }) => ({
        options: printerSelectOptions.value,
        placeholder: t('请选择打印机'),
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      }),
      rules: [
        {
          required: true,
          message: t('请选择打印机'),
        },
      ],
    },
    {
      label: t('打码模型'),
      field: 'modelCode',
      component: 'Select',
      componentProps: ({ formModel }) => ({
        options: modelCatalogSelectOptions.value,
        placeholder: t('请选择要打码的模型'),
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      }),
      rules: [
        {
          required: true,
          message: t('请选择模型'),
        },
      ],
    },
    {
      label: t('打印数量'),
      field: 'printNum',
      component: 'Input',
      componentProps: {
        placeholder: t('请输入打印数量'),
      },
      rules: [
        {
          pattern: /^[1-9]\d*$/,
          message: t('请输入一个正整数'),
          required: true,
        },
      ],
    },
    {
      label: t('打印类型'),
      field: 'printType',
      component: 'Select',
      componentProps: {
        placeholder: t('请选择打印类型'),
        dictType: 'model_print_type',
        allowClear: true,
      },
      rules: [
        {
          required: true,
          message: t('请选择打印机'),
        },
      ],
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { md: 24, lg: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    await fetchPrinter();
    await fetchModelCatalog();
    const res = await trackPrintLogForm(data);
    record.value = (res.trackPrintLog || {}) as TrackPrintLog;
    record.value.__t = new Date().getTime();
    setFieldsValue({
      ...record.value,
      //printer: record.value.printer?.toString() || null,
      modelCatalog: record.value.modelCatalog?.toString() || null,
    });
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        printCode: record.value.printCode,
      };
      const res = await trackPrintLogSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
