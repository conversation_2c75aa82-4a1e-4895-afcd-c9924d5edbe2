<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'trackprintlog:trackPrintLog:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('追踪码打印') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ printCode: record.printCode })">
          {{ record.printerName }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsTrackprintlogTrackPrintLogList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { trackPrintLogDelete, trackPrintLogListData } from '/@/api/ignite/trackPrintLog';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('trackprintlog.trackPrintLog');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('打印记录信息管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
      {
        label: t('打印机'),
        field: 'printerName',
        component: 'Input',
      },
      {
        label: t('模型'),
        field: 'carType',
        component: 'Input',
      },
      {
        label: t('打印类型'),
        field: 'printType',
        component: 'Select',
        componentProps: {
          dictType: 'model_print_type',
          allowClear: true,
        },
      },
      {
        label: t('打印状态'),
        field: 'printState',
        component: 'Select',
        componentProps: {
          dictType: 'model_print_state',
          allowClear: true,
        },
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('打印机'),
      dataIndex: 'printerName',
      key: 'a.printer_name',
      sorter: true,
      width: 180,
      align: 'center',
      slot: 'firstColumn',
    },
    {
      title: t('模型'),
      dataIndex: 'carType',
      key: 'a.car_type',
      sorter: true,
      width: 150,
      align: 'center',
      // 添加自定义渲染函数
      customRender: ({ record }) => {
        return `${record.modelNum || ''}${record.carType ? `-${record.carType}` : ''}${record.painting ? `-${record.painting}` : ''}`;
      }
    },
    {
      title: t('打印数量'),
      dataIndex: 'printNum',
      key: 'a.print_num',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('打印类型'),
      dataIndex: 'printType',
      key: 'a.print_type',
      sorter: true,
      width: 150,
      align: 'center',
      dictType: 'model_print_type',
    },
    {
      title: t('批次'),
      dataIndex: 'batch',
      key: 'a.batch',
      sorter: true,
      width: 100,
      align: 'center',
    },
    {
      title: t('打印时间'),
      dataIndex: 'printDate',
      key: 'a.print_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('打印状态'),
      dataIndex: 'printState',
      key: 'a.print_state',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'model_print_state',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'center',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 100,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑打印记录信息'),
        onClick: handleForm.bind(this, { printCode: record.printCode }),
        auth: 'trackprintlog:trackPrintLog:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: trackPrintLogListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const [registerDrawer, { openDrawer }] = useDrawer();

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { printCode: record.printCode };
    const res = await trackPrintLogDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
