<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable" @row-click="handleRowClick" />
    <a-button class="mt-2" @click="handleRowAdd" v-auth="'sys:empUser:edit'">
      <Icon icon="i-ant-design:plus-circle-outlined" /> {{ t('新增') }}
    </a-button>
  </div>
</template>
<script lang="ts" setup>
import { Ref, ref } from 'vue';
import { Icon } from '/@/components/Icon';
import { useI18n } from '/@/hooks/web/useI18n';
import { BasicTable, useTable } from '/@/components/Table';
import { trackBoxWithoutAgent } from '/@/api/ignite/trackBox';

const { t } = useI18n('ignite.trackBox');

// 保存下拉框选项
const trackBoxOptions = ref<any[]>([]);

// 初始化表格配置
const [registerTable, tableAction] = useTable({
  actionColumn: {
    width: 60,
    actions: (record: Recordable) => [
      {
        icon: 'i-ant-design:delete-outlined',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleRowDelete.bind(this, record),
        },
        auth: 'sys:empUser:edit',
      },
    ],
  },
  columns: [
    {
      title: t('箱编码信息'),
      dataIndex: 'trackBoxInformation',
      width: 300,
      align: 'center',
      // 关键修复：启用行编辑模式
      editRow: true,
      editComponent: 'Select',
      editComponentProps: {
        // 使用动态选项
        options: trackBoxOptions,
        showSearch: true,
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        }
      },
      editRule: {
        required: true,
        message: t('请选择箱编码信息'),
        trigger: 'blur',
      },
    },
  ],
  rowKey: 'id',
  pagination: false,
  bordered: true,
  size: 'small',
  inset: true,
});

// 加载箱编码选项
async function loadTrackBoxOptions() {
  try {
    const res = await trackBoxWithoutAgent();
    trackBoxOptions.value = res.map(item => ({
      label: item.trackInformation,
      value: item.trackInformationCode
    }));
  } catch (error) {
    console.error('加载箱编码选项失败:', error);
  }
}

function handleRowClick(data: Recordable) {
  // 点击行时进入编辑模式
  data.onEdit?.(true, false);
}

function handleRowAdd() {
  // 添加新行并进入编辑模式
  tableAction.insertTableDataRecord({
    id: 'rid_' + new Date().getTime(),
    isNewRecord: true,
    editable: true,
    trackBoxInformation: '' // 添加空字段用于编辑
  }, { index: 0 }); // 添加到顶部

  // 确保下拉框选项已加载
  if (trackBoxOptions.value.length === 0) {
    loadTrackBoxOptions();
  }
}

function handleRowDelete(data: Recordable) {
  tableAction.deleteTableDataRecord(data.id);
}

async function getTableData(data: Recordable): Promise<Recordable> {
  let valid = true;
  let tableList: Recordable[] = tableAction.getDataSource();

  // 验证所有行数据
  for (const record of tableAction.getDataSource()) {
    if (!(await record.onEdit?.(false, true))) {
      valid = false;
    }
  }

  if (!valid) {
    throw {
      errorFields: [{ name: ['trackBoxList'] }],
      message: t('箱编码信息填写有误，请根据提示修正'),
    };
  }

  // 将表格数据赋值给主对象的 trackBoxList 属性
  data.trackBoxList = tableList.map(item => ({
    id: item.id,
    trackBoxInformation: item.trackBoxInformation
  }));

  return tableList;
}

async function setTableData(
  data: Recordable,
  ctrlPermi: Ref<string>
) {
  // 确保下拉框选项已加载
  if (trackBoxOptions.value.length === 0) {
    await loadTrackBoxOptions();
  }

  // 设置表格数据
  const tableData = (data.trackBoxList || []).map(item => ({
    ...item,
    editable: true // 设置为可编辑状态
  }));

  tableAction.setTableData(tableData);
}

function clearTableData() {
  tableAction.setTableData([]);
  tableAction.setSelectedRowKeys([]);
}

// 初始化时加载选项
loadTrackBoxOptions();

defineExpose({
  setTableData,
  clearTableData,
  getTableData
});
</script>
