<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'agent:agent:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsAgentAgentForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { Agent, agentSave, agentForm } from '/@/api/ignite/agent';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('agent.agent');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<Agent>({} as Agent);

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('新增代理管理') : t('编辑代理管理'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('代理商名'),
      field: 'agentName',
      component: 'Input',
      componentProps: {
        maxlength: 64,
      },
    },
    {
      label: t('代理商手机'),
      field: 'agentPhone',
      component: 'Input',
      componentProps: {
        maxlength: 15,
      },
      rules: [
        {
          pattern: /(^0[1-9]{1}\d{8,10}$)|(^1[3,4,5,6,7,8,9]\d{9}$)/g,
          message: t('请输入固话或手机号码'),
        },
      ],
    },
    {
      label: t('代理地址'),
      field: 'agentAddress',
      component: 'Input',
      componentProps: {
        maxlength: 255,
      },
    },
    {
      label: t('加盟时间'),
      field: 'joinDate',
      component: 'DatePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        showTime: false,
      },
    },
    {
      label: t('备注'),
      field: 'remarks',
      component: 'InputTextArea',
      componentProps: {
        maxlength: 255,
      },
      colProps: { md: 24, lg: 24 },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 12 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    const res = await agentForm(data);
    record.value = (res.agent || {}) as Agent;
    record.value.__t = new Date().getTime();
    setFieldsValue(record.value);
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        agentCode: record.value.agentCode,
      };
      // console.log('submit', params, data, record);
      const res = await agentSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
