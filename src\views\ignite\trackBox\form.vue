<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    :showFooter="true"
    :okAuth="'trackbox:trackBox:edit'"
    @register="registerDrawer"
    @ok="handleSubmit"
    width="60%"
  >
    <template #title>
      <Icon :icon="getTitle.icon" class="m-1 pr-1" />
      <span> {{ getTitle.value }} </span>
    </template>
    <BasicForm @register="registerForm">
      <template #trackBoxList>
        <FormTrackBoxList ref="formTrackBoxListRef" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup name="ViewsTrackboxTrackBoxForm">
  import { ref, unref, computed } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import {
    TrackBox,
    trackBoxSave,
    trackBoxForm,
    trackBoxSendGoods,
    trackBoxWithoutAgent,
  } from '/@/api/ignite/trackBox';
  import FormTrackBoxList from './formTrackBoxList.vue';
  import { agent } from '/@/api/ignite/agent';

  const emit = defineEmits(['success', 'register']);

  const { t } = useI18n('trackbox.trackBox');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);
  const record = ref<TrackBox>({} as TrackBox);
  const formTrackBoxListRef = ref<InstanceType<typeof FormTrackBoxList>>();

  // 存储代理商选项
  const agentOptions = ref<{ label: string; value: string }[]>([]);
  const agentSelectOptions = computed(() => agentOptions.value);

  // 获取代理商数据
  async function fetchAgent() {
    try {
      const response = await agent();
      const agentsData = response.agentList || [];
      agentOptions.value = agentsData.map((type) => ({
        label: type.agentName,
        value: type.agentCode,
      }));
    } catch (error) {
      agentOptions.value = [];
    }
  }

  const getTitle = computed(() => ({
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: record.value.isNewRecord ? t('发货发货发货') : t('编辑箱追踪码信息'),
  }));

  const inputFormSchemas: FormSchema[] = [
    {
      label: t('代理编码'),
      field: 'agentCode',
      component: 'Select',
      componentProps: ({ formModel }) => ({
        options: agentSelectOptions.value,
        placeholder: t('请选择代理商'),
        allowClear: true,
        showSearch: true,
        filterOption: (input: string, option: any) =>
          option.label.toLowerCase().includes(input.toLowerCase()),
      }),
      rules: [
        {
          required: true,
          message: t('请选择代理商'),
        },
      ],
    },
    {
      label: t('箱码信息'),
      field: 'employeeInfo',
      component: 'FormGroup',
      colProps: { md: 24, lg: 24 },
    },
    {
      label: t('编码信息'),
      field: 'trackInformation',
      component: 'Input',
      slot: 'trackBoxList',
      componentProps: {
        maxlength: 10,
      },
    },
  ];

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 120,
    schemas: inputFormSchemas,
    baseColProps: { md: 24, lg: 24 },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    setDrawerProps({ loading: true });
    await resetFields();
    await fetchAgent();
    formTrackBoxListRef.value?.clearTableData();
    setDrawerProps({ loading: true });
    const res = await trackBoxForm(data);
    record.value = (res.trackBox || {}) as TrackBox;
    record.value.__t = new Date().getTime();
    setFieldsValue({
      ...record.value,
      agent: record.value.agent?.toString() || null,
    });
    setDrawerProps({ loading: false });
  });

  async function handleSubmit() {
    try {
      const data = await validate();
      setDrawerProps({ confirmLoading: true });
      const params: any = {
        isNewRecord: record.value.isNewRecord,
        agentCode: record.value.agentCode,
      };
      const res = await trackBoxSave(params, data);
      showMessage(res.message);
      setTimeout(closeDrawer);
      emit('success', data);
    } catch (error: any) {
      if (error && error.errorFields) {
        showMessage(error.message || t('common.validateError'));
      }
      console.log('error', error);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
