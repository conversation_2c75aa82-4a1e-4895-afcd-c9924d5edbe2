<!--
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
-->
<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Icon :icon="getTitle.icon" class="m-1 pr-1" />
        <span> {{ getTitle.value }} </span>
      </template>
      <template #toolbar>
        <a-button type="primary" @click="handleForm({})" v-auth="'trackmodel:trackModel:edit'">
          <Icon icon="i-fluent:add-12-filled" /> {{ t('新增') }}
        </a-button>
      </template>
      <template #firstColumn="{ record }">
        <a @click="handleForm({ trackCode: record.trackCode })">
          {{ record.agentCode }}
        </a>
      </template>
    </BasicTable>
    <InputForm @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="ViewsTrackmodelTrackModelList">
  import { unref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { router } from '/@/router';
  import { Icon } from '/@/components/Icon';
  import { BasicTable, BasicColumn, useTable } from '/@/components/Table';
  import { trackModelDelete, trackModelListData } from '/@/api/ignite/trackModel';
  import { useDrawer } from '/@/components/Drawer';
  import { FormProps } from '/@/components/Form';
  import InputForm from './form.vue';

  const { t } = useI18n('trackmodel.trackModel');
  const { showMessage } = useMessage();
  const { meta } = unref(router.currentRoute);

  const getTitle = {
    icon: meta.icon || 'i-ant-design:book-outlined',
    value: meta.title || t('模型追踪码信息管理'),
  };

  const searchForm: FormProps = {
    baseColProps: { md: 8, lg: 6 },
    labelWidth: 90,
    schemas: [
      {
        label: t('经销商编码'),
        field: 'agentCode',
        component: 'Input',
      },
      {
        label: t('件编码'),
        field: 'pieceCode',
        component: 'Input',
      },
      {
        label: t('模型编码'),
        field: 'modelCode',
        component: 'Input',
      },
      {
        label: t('批次'),
        field: 'batch',
        component: 'Input',
      },
      {
        label: t('追溯码信息'),
        field: 'trackInformation',
        component: 'Input',
      },
      {
        label: t('打包时间'),
        field: 'packDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('发货时间'),
        field: 'shipDate',
        component: 'DatePicker',
        componentProps: {
          format: 'YYYY-MM-DD HH:mm',
          showTime: { format: 'HH:mm' },
        },
      },
      {
        label: t('追溯码状态'),
        field: 'trackState',
        component: 'Input',
      },
      {
        label: t('状态'),
        field: 'status',
        component: 'Select',
        componentProps: {
          dictType: 'sys_search_status',
          allowClear: true,
          onChange: handleSuccess,
        },
      },
      {
        label: t('备注'),
        field: 'remarks',
        component: 'Input',
      },
    ],
  };

  const tableColumns: BasicColumn[] = [
    {
      title: t('经销商编码'),
      dataIndex: 'agentCode',
      key: 'a.agent_code',
      sorter: true,
      width: 230,
      align: 'left',
      slot: 'firstColumn',
    },
    {
      title: t('件编码'),
      dataIndex: 'pieceCode',
      key: 'a.piece_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('模型编码'),
      dataIndex: 'modelCode',
      key: 'a.model_code',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('批次'),
      dataIndex: 'batch',
      key: 'a.batch',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('追溯码信息'),
      dataIndex: 'trackInformation',
      key: 'a.track_information',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('打包时间'),
      dataIndex: 'packDate',
      key: 'a.pack_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('发货时间'),
      dataIndex: 'shipDate',
      key: 'a.ship_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('追溯码状态'),
      dataIndex: 'trackState',
      key: 'a.track_state',
      sorter: true,
      width: 130,
      align: 'left',
    },
    {
      title: t('状态'),
      dataIndex: 'status',
      key: 'a.status',
      sorter: true,
      width: 130,
      align: 'center',
      dictType: 'sys_search_status',
    },
    {
      title: t('更新时间'),
      dataIndex: 'updateDate',
      key: 'a.update_date',
      sorter: true,
      width: 130,
      align: 'center',
    },
    {
      title: t('备注'),
      dataIndex: 'remarks',
      key: 'a.remarks',
      sorter: true,
      width: 130,
      align: 'left',
    },
  ];

  const actionColumn: BasicColumn = {
    width: 160,
    actions: (record: Recordable) => [
      {
        icon: 'i-clarity:note-edit-line',
        title: t('编辑模型追踪码信息'),
        onClick: handleForm.bind(this, { trackCode: record.trackCode }),
        auth: 'trackmodel:trackModel:edit',
      },
    ],
  };

  const [registerTable, { reload }] = useTable({
    api: trackModelListData,
    beforeFetch: (params) => {
      return params;
    },
    columns: tableColumns,
    actionColumn: actionColumn,
    formConfig: searchForm,
    showTableSetting: true,
    useSearchForm: true,
    canResize: true,
  });

  const [registerDrawer, { openDrawer }] = useDrawer();

  function handleForm(record: Recordable) {
    openDrawer(true, record);
  }

  async function handleDelete(record: Recordable) {
    const params = { trackCode: record.trackCode };
    const res = await trackModelDelete(params);
    showMessage(res.message);
    handleSuccess(record);
  }

  function handleSuccess(record: Recordable) {
    reload({ record });
  }
</script>
