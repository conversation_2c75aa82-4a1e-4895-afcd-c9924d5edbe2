/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface Printer extends BasicModel<Printer> {
  printerCode?: string; // 打印机主键
  printerName?: string; // 打印机名称
  printerBianma?: string; // 打印机编码
}

export const printerList = (params?: Printer | any) =>
  defHttp.get<Printer>({ url: adminPath + '/printer/printer/list', params });

export const printerListData = (params?: Printer | any) =>
  defHttp.post<Page<Printer>>({ url: adminPath + '/printer/printer/listData', params });

export const printerForm = (params?: Printer | any) =>
  defHttp.get<Printer>({ url: adminPath + '/printer/printer/form', params });

export const printerSave = (params?: any, data?: Printer | any) =>
  defHttp.postJson<Printer>({ url: adminPath + '/printer/printer/save', params, data });

export const printerDelete = (params?: Printer | any) =>
  defHttp.get<Printer>({ url: adminPath + '/printer/printer/delete', params });

export const printer = (params?: Printer | any) =>
  defHttp.get<Printer>({ url: adminPath + '/printer/printer/getPrinterList', params });
