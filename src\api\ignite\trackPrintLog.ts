/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface TrackPrintLog extends BasicModel<TrackPrintLog> {
  printCode?: string; // 主键
  printerCode?: string; // 打印机编码
  printerName?: string; //打印机名称
  modelCode?: string; // 模型主键
  modelNum?: string; //模型编码
  painting?: string; //颜色/涂装
  carType?: string; //车型
  printNum?: number; // 打印数量
  batch?: number; // 批次
  printDate?: string; // 打印时间
  printType?: string; // 打印类型（0箱 1模型）
  printState?: string; // 打印状态（0已完成 1已取消 2打印错误）
}

export const trackPrintLogList = (params?: TrackPrintLog | any) =>
  defHttp.get<TrackPrintLog>({ url: adminPath + '/trackprintlog/trackPrintLog/list', params });

export const trackPrintLogListData = (params?: TrackPrintLog | any) =>
  defHttp.post<Page<TrackPrintLog>>({ url: adminPath + '/trackprintlog/trackPrintLog/listData', params });

export const trackPrintLogForm = (params?: TrackPrintLog | any) =>
  defHttp.get<TrackPrintLog>({ url: adminPath + '/trackprintlog/trackPrintLog/form', params });

export const trackPrintLogSave = (params?: any, data?: TrackPrintLog | any) =>
  defHttp.postJson<TrackPrintLog>({ url: adminPath + '/trackprintlog/trackPrintLog/save', params, data });

export const trackPrintLogDelete = (params?: TrackPrintLog | any) =>
  defHttp.get<TrackPrintLog>({ url: adminPath + '/trackprintlog/trackPrintLog/delete', params });
