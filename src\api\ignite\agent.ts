/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface Agent extends BasicModel<Agent> {
  agentCode?: string; // 主键
  agentName?: string; // 代理商名
  agentPhone?: string; // 代理商手机
  agentPoints?: number; // 代理商积分
  agentAddress?: string; // 代理地址
  joinDate?: string; // 加盟时间
}

export const agentList = (params?: Agent | any) =>
  defHttp.get<Agent>({ url: adminPath + '/agent/agent/list', params });

export const agentListData = (params?: Agent | any) =>
  defHttp.post<Page<Agent>>({ url: adminPath + '/agent/agent/listData', params });

export const agentForm = (params?: Agent | any) =>
  defHttp.get<Agent>({ url: adminPath + '/agent/agent/form', params });

export const agentSave = (params?: any, data?: Agent | any) =>
  defHttp.postJson<Agent>({ url: adminPath + '/agent/agent/save', params, data });

export const agentDelete = (params?: Agent | any) =>
  defHttp.get<Agent>({ url: adminPath + '/agent/agent/delete', params });

export const agent = (params?: Agent | any) =>
  defHttp.get<Agent>({ url: adminPath + '/agent/agent/getAgentList', params });
