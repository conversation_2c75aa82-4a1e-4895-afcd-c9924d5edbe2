{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"baseUrl": ".", "target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "useUnknownInCatchVariables": false, "composite": false, "declarationMap": false, "inlineSources": false, "isolatedModules": true, "preserveWatchOutput": true, "removeComments": true, "skipLibCheck": true, "noImplicitAny": false, "strictPropertyInitialization": true, "strictBindCallApply": true, "strictNullChecks": true, "noUnusedLocals": false, "noUnusedParameters": false, "experimentalDecorators": true, "resolveJsonModule": true, "declaration": false, "jsx": "preserve", "jsxImportSource": "vue", "useDefineForClassFields": true, "lib": ["ESNext", "DOM"], "types": ["vite/client"], "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"]}}, "include": ["build/**/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "types/*.ts", "uno.config.ts", "vite.config.ts"], "exclude": ["tests/server/**/*.ts", "node_modules", "**/*.js", "dist"]}