@import './pagination.less';
@import './input.less';
@import './btn.less';
// @import './table.less';

.ant-image-preview-root {
  img {
    display: unset;
  }
}

// span.anticon:not(.jeesite-icon) {
//   vertical-align: 0.125em !important;
// }

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgb(0 0 0 / 30%);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  }
}

.ant-modal > div[aria-hidden='true'] {
  display: none !important;
}

// =================================
// ========= modal message =========
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

.modal-posfull-content {
  max-height: 300px;
  overflow-y: auto;
}

// =================================
// ======= common components =======
// =================================
.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}

.ant-tree,
.ant-select-tree {
  // &-list-holder {
  //   overflow-x: hidden !important;
  // }
  &-list-scrollbar-show {
    display: block !important;
    width: 6px !important;
  }

  &-list-scrollbar-thumb {
    background: rgb(0 0 0 / 20%) !important;
  }
}

.ant-tag {
  font-size: 14px !important;
}

html[data-theme='dark'] {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: rgb(255 255 255 / 65%);
  }

  .ant-tree,
  .ant-select-tree {
    &-list-scrollbar-thumb {
      background: rgb(255 255 255 / 20%) !important;
    }
  }
}

// fix unocss for login.vue
@media (max-width: @screen-md) {
  .md\:hidden {
    display: none;
  }
}
