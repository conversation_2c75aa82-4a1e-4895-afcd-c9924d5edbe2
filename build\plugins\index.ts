/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { type PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import vitePluginCertificate from 'vite-plugin-mkcert';
import { appConfigPlugin } from '../config/appConfig';
import { configCompressPlugin } from './compress';
import { configHtmlPlugin } from './html';
import { configLegacyPlugin } from './legacy';
import { configThemePlugin } from '../theme';
import { configUnoCSSPlugin } from './unocss';
import { configVisualizerPlugin } from './visualizer';
import { PackageJson } from 'pkg-types';
import { visualizer } from 'rollup-plugin-visualizer'

export function createVitePlugins(isBuild: boolean, viteEnv: ViteEnv, pkg: PackageJson) {
  const vitePlugins: PluginOption[] = [
    vue(),
    vueJsx(),
    vueSetupExtend(),
    vitePluginCertificate({
      source: 'coding',
    }),
  ];
  if (isBuild) {
    vitePlugins.push(
      visualizer({
        open: true,
        filename: 'stats.html',
      })
    )
  }

  // app-config-plugin
  vitePlugins.push(appConfigPlugin(isBuild, viteEnv, pkg));

  // UnoCSS-vite-plugin
  vitePlugins.push(configUnoCSSPlugin());

  // vite-plugin-html
  vitePlugins.push(configHtmlPlugin(isBuild));

  // rollup-plugin-visualizer
  vitePlugins.push(configVisualizerPlugin());

  // vite-plugin-theme-vite3
  vitePlugins.push(configThemePlugin(isBuild));

  // rollup-plugin-gzip
  vitePlugins.push(configCompressPlugin(isBuild, viteEnv));

  // @vitejs/plugin-legacy
  vitePlugins.push(configLegacyPlugin(isBuild, viteEnv));

  return vitePlugins;
}

export {
  appConfigPlugin,
  configCompressPlugin,
  configHtmlPlugin,
  configLegacyPlugin,
  configThemePlugin,
  configUnoCSSPlugin,
  configVisualizerPlugin,
};
