/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 * <AUTHOR>
 */
import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
import { BasicModel, Page } from '/@/api/model/baseModel';

const { adminPath } = useGlobSetting();

export interface TrackModel extends BasicModel<TrackModel> {
  trackCode?: string; // 主键
  agentCode?: string; // 经销商编码
  agentName?: string; // 经销商名
  pieceCode?: string; // 箱编码
  modelCode?: string; // 模型编码
  modelNum?: string; // 模型编号
  carType?: string; // 车型
  carBir?: string; // 年份
  painting?: string; // 涂装
  batch?: string; // 批次
  trackInformation?: string; // 追溯码信息
  packDate?: string; // 打包时间
  shipDate?: string; // 发货时间
  trackState?: string; // 追溯码状态（0待装箱 1已装箱 2已发货）
}

export interface BindTrackList extends BasicModel<BindTrackList> {
  trackBox?: string; // 箱编码信息
  trackModelList?: string[]; // 模型编码信息
}

export const trackModelList = (params?: TrackModel | any) =>
  defHttp.get<TrackModel>({ url: adminPath + '/trackmodel/trackModel/list', params });

export const trackModelListData = (params?: TrackModel | any) =>
  defHttp.post<Page<TrackModel>>({ url: adminPath + '/trackmodel/trackModel/listData', params });

export const trackModelForm = (params?: TrackModel | any) =>
  defHttp.get<TrackModel>({ url: adminPath + '/trackmodel/trackModel/form', params });

export const trackModelSave = (params?: any, data?: TrackModel | any) =>
  defHttp.postJson<TrackModel>({ url: adminPath + '/trackmodel/trackModel/save', params, data });

export const trackModelDelete = (params?: TrackModel | any) =>
  defHttp.get<TrackModel>({ url: adminPath + '/trackmodel/trackModel/delete', params });

//箱追踪码、模型追踪码绑定
export const bindTrack = (params?: any, data?: BindTrackList | any) =>
  defHttp.postJson<BindTrackList>({ url: adminPath + '/trackmodel/trackModel/bindTrackInformation', params, data });
